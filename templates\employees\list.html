{% extends "base.html" %}

{% block title %}قائمة العمال - نظام إدارة الخدمات الاجتماعية{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">قائمة العمال</h1>
        <p class="text-muted">إدارة وعرض بيانات العمال</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('add_employee') }}" class="btn btn-primary">
            <i class="bi bi-person-plus"></i> إضافة عامل جديد
        </a>
    </div>
</div>

<!-- Search Form -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-search"></i> البحث عن العمال
                </h5>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ url_for('employees') }}">
                    <div class="row">
                        <div class="col-md-4">
                            {{ search_form.search_type.label(class="form-label") }}
                            {{ search_form.search_type(class="form-select") }}
                        </div>
                        <div class="col-md-6">
                            {{ search_form.search_term.label(class="form-label") }}
                            {{ search_form.search_term(class="form-control", value=request.args.get('search_term', '')) }}
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="bi bi-search"></i> بحث
                            </button>
                        </div>
                    </div>
                </form>
                
                {% if request.args.get('search_term') %}
                <div class="mt-3">
                    <a href="{{ url_for('employees') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="bi bi-x-circle"></i> إلغاء البحث
                    </a>
                    <span class="text-muted ms-2">
                        نتائج البحث عن: <strong>{{ request.args.get('search_term') }}</strong>
                    </span>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Employees Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-people"></i> العمال
                    <span class="badge bg-primary">{{ employees.total }}</span>
                </h5>
            </div>
            <div class="card-body">
                {% if employees.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>الاسم الكامل</th>
                                <th>رقم العامل</th>
                                <th>الرقم الوطني</th>
                                <th>الرتبة</th>
                                <th>مكان العمل</th>
                                <th>الهاتف</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in employees.items %}
                            <tr>
                                <td>
                                    {% if employee.photo_filename %}
                                        <img src="{{ url_for('static', filename='uploads/employees/' + employee.photo_filename) }}" 
                                             alt="صورة {{ employee.full_name }}" class="employee-photo">
                                    {% else %}
                                        <div class="employee-photo bg-secondary d-flex align-items-center justify-content-center">
                                            <i class="bi bi-person text-white"></i>
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>{{ employee.full_name }}</strong><br>
                                    <small class="text-muted">{{ employee.social_security_number }}</small>
                                </td>
                                <td>{{ employee.employee_number }}</td>
                                <td>{{ employee.national_id }}</td>
                                <td>{{ employee.rank.name }}</td>
                                <td>{{ employee.workplace.name }}</td>
                                <td>{{ employee.phone or '-' }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('view_employee', id=employee.id) }}" 
                                           class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="{{ url_for('edit_employee', id=employee.id) }}" 
                                           class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                title="حذف" onclick="confirmDelete({{ employee.id }}, '{{ employee.full_name }}')">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if employees.pages > 1 %}
                <nav aria-label="تصفح الصفحات">
                    <ul class="pagination justify-content-center">
                        {% if employees.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('employees', page=employees.prev_num, **request.args) }}">السابق</a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in employees.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != employees.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('employees', page=page_num, **request.args) }}">{{ page_num }}</a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if employees.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('employees', page=employees.next_num, **request.args) }}">التالي</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-people display-1 text-muted"></i>
                    <h4 class="mt-3">لا توجد عمال</h4>
                    <p class="text-muted">لم يتم العثور على أي عمال مطابقين لمعايير البحث</p>
                    <a href="{{ url_for('add_employee') }}" class="btn btn-primary">
                        <i class="bi bi-person-plus"></i> إضافة عامل جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف العامل <strong id="employeeName"></strong>؟</p>
                <p class="text-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    تحذير: سيتم حذف جميع البيانات المرتبطة بهذا العامل (الأطفال، الحسابات البنكية، إلخ)
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(employeeId, employeeName) {
    document.getElementById('employeeName').textContent = employeeName;
    document.getElementById('deleteForm').action = '/employee/' + employeeId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}

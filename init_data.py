from app import app, db
from models import Rank, Workplace, EducationLevel

def init_database():
    """إنشاء البيانات الأولية لقاعدة البيانات"""
    
    with app.app_context():
        # إنشاء الجداول
        db.create_all()
        
        # إضافة الرتب الوظيفية
        ranks = [
            'مدير عام',
            'مدير',
            'رئيس مصلحة',
            'رئيس مكتب',
            'مفتش',
            'محافظ حسابات',
            'مهندس دولة',
            'مهندس تطبيق',
            'تقني سامي',
            'تقني',
            'مساعد تقني',
            'كاتب مديرية',
            'كاتب',
            'عون إدارة',
            'عون خدمة',
            'حارس',
            'سائق'
        ]
        
        for rank_name in ranks:
            if not Rank.query.filter_by(name=rank_name).first():
                rank = Rank(name=rank_name)
                db.session.add(rank)
        
        # إضافة أماكن العمل
        workplaces = [
            ('ولاية الجلفة', 'ولاية'),
            ('دائرة الجلفة', 'دائرة'),
            ('دائرة عين وسارة', 'دائرة'),
            ('دائرة حاسي بحبح', 'دائرة'),
            ('دائرة مسعد', 'دائرة'),
            ('دائرة الإدريسية', 'دائرة'),
            ('دائرة عمورة', 'دائرة'),
            ('دائرة فيض البطمة', 'دائرة'),
            ('دائرة سيدي لعجال', 'دائرة'),
            ('دائرة الشارف', 'دائرة'),
            ('دائرة دار الشيوخ', 'دائرة'),
            ('دائرة بنهار', 'دائرة'),
            ('دائرة زكار', 'دائرة')
        ]
        
        for workplace_name, workplace_type in workplaces:
            if not Workplace.query.filter_by(name=workplace_name).first():
                workplace = Workplace(name=workplace_name, type=workplace_type)
                db.session.add(workplace)
        
        # إضافة المستويات التعليمية
        education_levels = [
            ('لم يلتحق بالمدرسة', 0),
            ('التحضيري', 1),
            ('السنة الأولى ابتدائي', 2),
            ('السنة الثانية ابتدائي', 3),
            ('السنة الثالثة ابتدائي', 4),
            ('السنة الرابعة ابتدائي', 5),
            ('السنة الخامسة ابتدائي', 6),
            ('السنة الأولى متوسط', 7),
            ('السنة الثانية متوسط', 8),
            ('السنة الثالثة متوسط', 9),
            ('السنة الرابعة متوسط', 10),
            ('السنة الأولى ثانوي', 11),
            ('السنة الثانية ثانوي', 12),
            ('السنة الثالثة ثانوي', 13),
            ('جامعي - السنة الأولى', 14),
            ('جامعي - السنة الثانية', 15),
            ('جامعي - السنة الثالثة', 16),
            ('جامعي - ماستر 1', 17),
            ('جامعي - ماستر 2', 18),
            ('دكتوراه', 19),
            ('تخرج', 20)
        ]
        
        for level_name, order in education_levels:
            if not EducationLevel.query.filter_by(name=level_name).first():
                level = EducationLevel(name=level_name, order=order)
                db.session.add(level)
        
        # حفظ التغييرات
        db.session.commit()
        print("تم إنشاء البيانات الأولية بنجاح!")

if __name__ == '__main__':
    init_database()

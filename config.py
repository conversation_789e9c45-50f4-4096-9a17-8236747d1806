#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
إعدادات التطبيق
"""

import os
from datetime import timedelta

class Config:
    """الإعدادات الأساسية"""
    
    # المفتاح السري للتطبيق
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'sosiel-djelfa-2025-secret-key-change-in-production'
    
    # إعدادات قاعدة البيانات
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///social_services.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # إعدادات رفع الملفات
    UPLOAD_FOLDER = 'static/uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
    
    # إعدادات الجلسة
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    
    # إعدادات التطبيق
    APP_NAME = 'نظام إدارة الخدمات الاجتماعية - ولاية الجلفة'
    APP_VERSION = '1.0.0'
    
    # إعدادات التصفح
    ITEMS_PER_PAGE = 20
    
    # إعدادات الصور
    IMAGE_THUMBNAIL_SIZE = (300, 300)
    
    @staticmethod
    def init_app(app):
        """تهيئة التطبيق"""
        # إنشاء مجلدات الرفع
        upload_folders = [
            app.config['UPLOAD_FOLDER'],
            os.path.join(app.config['UPLOAD_FOLDER'], 'employees'),
            os.path.join(app.config['UPLOAD_FOLDER'], 'children')
        ]
        
        for folder in upload_folders:
            os.makedirs(folder, exist_ok=True)

class DevelopmentConfig(Config):
    """إعدادات التطوير"""
    DEBUG = True
    
class ProductionConfig(Config):
    """إعدادات الإنتاج"""
    DEBUG = False
    
    # استخدام PostgreSQL في الإنتاج
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'postgresql://username:password@localhost/social_services'

class TestingConfig(Config):
    """إعدادات الاختبار"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False

# قاموس الإعدادات
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed
from wtforms import <PERSON>Field, IntegerField, SelectField, DateField, TextAreaField, SubmitField, BooleanField
from wtforms.validators import DataRequired, Length, Optional, Regexp
from wtforms.widgets import TextArea
from models import Rank, Workplace, EducationLevel

class EmployeeForm(FlaskForm):
    """نموذج إضافة/تعديل العامل"""
    
    # البيانات الشخصية
    first_name = StringField('الاسم الأول', validators=[DataRequired(), Length(min=2, max=100)])
    last_name = StringField('اللقب', validators=[DataRequired(), Length(min=2, max=100)])
    national_id = StringField('الرقم الوطني', validators=[
        DataRequired(), 
        Length(min=18, max=18),
        Regexp(r'^\d{18}$', message='الرقم الوطني يجب أن يحتوي على 18 رقم فقط')
    ])
    social_security_number = StringField('رقم الضمان الاجتماعي', validators=[
        DataRequired(), 
        Length(min=10, max=20)
    ])
    birth_date = DateField('تاريخ الميلاد', validators=[DataRequired()])
    phone = StringField('رقم الهاتف', validators=[Optional(), Length(max=15)])
    address = TextAreaField('العنوان', validators=[Optional()], widget=TextArea())
    
    # البيانات الوظيفية
    employee_number = StringField('رقم العامل', validators=[DataRequired(), Length(min=1, max=20)])
    rank_id = SelectField('الرتبة', coerce=int, validators=[DataRequired()])
    workplace_id = SelectField('مكان العمل', coerce=int, validators=[DataRequired()])
    hire_date = DateField('تاريخ التوظيف', validators=[DataRequired()])
    
    # الصورة
    photo = FileField('صورة العامل', validators=[
        Optional(),
        FileAllowed(['jpg', 'jpeg', 'png', 'gif'], 'الصور فقط!')
    ])
    
    submit = SubmitField('حفظ')
    
    def __init__(self, *args, **kwargs):
        super(EmployeeForm, self).__init__(*args, **kwargs)
        self.rank_id.choices = [(0, 'اختر الرتبة')] + [(r.id, r.name) for r in Rank.query.all()]
        self.workplace_id.choices = [(0, 'اختر مكان العمل')] + [(w.id, w.name) for w in Workplace.query.all()]

class ChildForm(FlaskForm):
    """نموذج إضافة/تعديل الطفل"""
    
    first_name = StringField('الاسم الأول', validators=[DataRequired(), Length(min=2, max=100)])
    last_name = StringField('اللقب', validators=[DataRequired(), Length(min=2, max=100)])
    birth_date = DateField('تاريخ الميلاد', validators=[DataRequired()])
    gender = SelectField('الجنس', choices=[('', 'اختر الجنس'), ('ذكر', 'ذكر'), ('أنثى', 'أنثى')], validators=[DataRequired()])
    
    # البيانات الدراسية
    education_level_id = SelectField('المستوى التعليمي', coerce=int, validators=[Optional()])
    school_name = StringField('اسم المدرسة', validators=[Optional(), Length(max=200)])
    academic_year = StringField('السنة الدراسية', validators=[Optional(), Length(max=20)])
    
    # الصورة
    photo = FileField('صورة الطفل', validators=[
        Optional(),
        FileAllowed(['jpg', 'jpeg', 'png', 'gif'], 'الصور فقط!')
    ])
    
    submit = SubmitField('حفظ')
    
    def __init__(self, *args, **kwargs):
        super(ChildForm, self).__init__(*args, **kwargs)
        self.education_level_id.choices = [(0, 'اختر المستوى التعليمي')] + [(e.id, e.name) for e in EducationLevel.query.order_by(EducationLevel.order).all()]

class BankAccountForm(FlaskForm):
    """نموذج إضافة/تعديل الحساب البنكي"""
    
    account_type = SelectField('نوع الحساب', choices=[
        ('', 'اختر نوع الحساب'),
        ('بنكي', 'حساب بنكي'),
        ('بريدي', 'حساب بريدي')
    ], validators=[DataRequired()])
    
    account_number = StringField('رقم الحساب', validators=[DataRequired(), Length(min=5, max=50)])
    bank_name = StringField('اسم البنك', validators=[Optional(), Length(max=200)])
    branch_name = StringField('اسم الوكالة', validators=[Optional(), Length(max=200)])
    is_active = BooleanField('حساب نشط', default=True)
    
    submit = SubmitField('حفظ')

class SearchForm(FlaskForm):
    """نموذج البحث"""
    
    search_type = SelectField('نوع البحث', choices=[
        ('name', 'البحث بالاسم'),
        ('national_id', 'البحث بالرقم الوطني'),
        ('employee_number', 'البحث برقم العامل'),
        ('workplace', 'البحث بمكان العمل'),
        ('rank', 'البحث بالرتبة')
    ], default='name')
    
    search_term = StringField('كلمة البحث', validators=[DataRequired()])
    submit = SubmitField('بحث')

class RankForm(FlaskForm):
    """نموذج إضافة/تعديل الرتبة"""
    
    name = StringField('اسم الرتبة', validators=[DataRequired(), Length(min=2, max=100)])
    description = TextAreaField('الوصف', validators=[Optional()], widget=TextArea())
    submit = SubmitField('حفظ')

class WorkplaceForm(FlaskForm):
    """نموذج إضافة/تعديل مكان العمل"""
    
    name = StringField('اسم مكان العمل', validators=[DataRequired(), Length(min=2, max=200)])
    type = SelectField('النوع', choices=[
        ('', 'اختر النوع'),
        ('ولاية', 'ولاية'),
        ('دائرة', 'دائرة')
    ], validators=[DataRequired()])
    address = TextAreaField('العنوان', validators=[Optional()], widget=TextArea())
    submit = SubmitField('حفظ')

class EducationLevelForm(FlaskForm):
    """نموذج إضافة/تعديل المستوى التعليمي"""
    
    name = StringField('اسم المستوى', validators=[DataRequired(), Length(min=2, max=100)])
    order = IntegerField('الترتيب', validators=[DataRequired()])
    submit = SubmitField('حفظ')

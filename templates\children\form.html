{% extends "base.html" %}

{% block title %}{{ title }} - نظام إدارة الخدمات الاجتماعية{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">{{ title }}</h1>
        <p class="text-muted">
            العامل: <strong>{{ employee.full_name }}</strong> ({{ employee.employee_number }})
        </p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('view_employee', id=employee.id) }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right"></i> العودة لصفحة العامل
        </a>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person-hearts"></i> بيانات الطفل
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <!-- الصورة الشخصية -->
                        <div class="col-md-3 mb-4">
                            <div class="text-center">
                                <div class="mb-3">
                                    {% if child and child.photo_filename %}
                                        <img src="{{ url_for('static', filename='uploads/children/' + child.photo_filename) }}" 
                                             alt="صورة {{ child.full_name }}" 
                                             class="img-thumbnail" 
                                             style="width: 200px; height: 200px; object-fit: cover;">
                                    {% else %}
                                        <div class="bg-light border rounded d-flex align-items-center justify-content-center" 
                                             style="width: 200px; height: 200px; margin: 0 auto;">
                                            <i class="bi bi-person display-1 text-muted"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.photo.label(class="form-label") }}
                                    {{ form.photo(class="form-control") }}
                                    {% if form.photo.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.photo.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- البيانات الشخصية -->
                        <div class="col-md-9">
                            <h6 class="text-primary mb-3">
                                <i class="bi bi-person-badge"></i> البيانات الشخصية
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    {{ form.first_name.label(class="form-label") }}
                                    {{ form.first_name(class="form-control") }}
                                    {% if form.first_name.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.first_name.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    {{ form.last_name.label(class="form-label") }}
                                    {{ form.last_name(class="form-control") }}
                                    {% if form.last_name.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.last_name.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    {{ form.birth_date.label(class="form-label") }}
                                    {{ form.birth_date(class="form-control") }}
                                    {% if form.birth_date.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.birth_date.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    {{ form.gender.label(class="form-label") }}
                                    {{ form.gender(class="form-select") }}
                                    {% if form.gender.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.gender.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <hr class="my-4">
                    
                    <!-- البيانات الدراسية -->
                    <h6 class="text-primary mb-3">
                        <i class="bi bi-book"></i> البيانات الدراسية
                    </h6>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            {{ form.education_level_id.label(class="form-label") }}
                            {{ form.education_level_id(class="form-select") }}
                            {% if form.education_level_id.errors %}
                                <div class="text-danger small">
                                    {% for error in form.education_level_id.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            {{ form.school_name.label(class="form-label") }}
                            {{ form.school_name(class="form-control") }}
                            {% if form.school_name.errors %}
                                <div class="text-danger small">
                                    {% for error in form.school_name.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            {{ form.academic_year.label(class="form-label") }}
                            {{ form.academic_year(class="form-control", placeholder="2023-2024") }}
                            {% if form.academic_year.errors %}
                                <div class="text-danger small">
                                    {% for error in form.academic_year.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <hr class="my-4">
                    
                    <!-- أزرار الحفظ -->
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('view_employee', id=employee.id) }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i> إلغاء
                        </a>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> {{ form.submit.label.text }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

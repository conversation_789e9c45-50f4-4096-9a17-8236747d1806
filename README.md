# نظام إدارة الخدمات الاجتماعية - ولاية الجلفة

نظام ويب متكامل لإدارة لجنة الخدمات الاجتماعية لعمال الولاية والدوائر بالجلفة، مطور بـ Python Flask.

## المميزات الرئيسية

### إدارة العمال
- ✅ تسجيل بيانات العمال الشخصية والوظيفية
- ✅ رفع وإدارة صور العمال
- ✅ تسجيل الرقم الوطني ورقم الضمان الاجتماعي
- ✅ تحديد الرتبة ومكان العمل
- ✅ البحث والتصفية المتقدمة

### إدارة الأطفال
- ✅ تسجيل أطفال العمال
- ✅ تتبع المستويات الدراسية
- ✅ رفع صور الأطفال
- ✅ تسجيل المدارس والسنوات الدراسية

### إدارة الحسابات المالية
- ✅ تسجيل الحسابات البنكية
- ✅ دعم الحسابات البريدية
- ✅ تسجيل أسماء البنوك والوكالات

### التقارير والإحصائيات
- ✅ إحصائيات شاملة للنظام
- ✅ تقارير حسب مكان العمل
- ✅ تقارير حسب المستوى التعليمي
- ✅ إحصائيات الأطفال والحسابات البنكية

## التقنيات المستخدمة

- **Backend**: Python Flask
- **Database**: SQLite (قابل للترقية لـ PostgreSQL/MySQL)
- **Frontend**: HTML5, CSS3, Bootstrap 5
- **Forms**: Flask-WTF, WTForms
- **Database ORM**: SQLAlchemy
- **Image Processing**: Pillow (PIL)
- **Icons**: Bootstrap Icons

## متطلبات النظام

- Python 3.8 أو أحدث
- pip (مدير حزم Python)

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd SOSIEL2025
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. إنشاء قاعدة البيانات والبيانات الأولية
```bash
python init_data.py
```

### 4. تشغيل التطبيق
```bash
python run.py
```

### 5. الوصول للتطبيق
افتح المتصفح وانتقل إلى: `http://localhost:5000`

## هيكل المشروع

```
SOSIEL2025/
├── app.py                 # التطبيق الرئيسي
├── models.py             # نماذج قاعدة البيانات
├── forms.py              # نماذج الإدخال
├── init_data.py          # البيانات الأولية
├── run.py                # ملف التشغيل
├── requirements.txt      # المتطلبات
├── templates/            # قوالب HTML
│   ├── base.html
│   ├── index.html
│   ├── employees/
│   ├── children/
│   ├── bank_accounts/
│   └── reports/
└── static/
    └── uploads/          # الصور المرفوعة
        ├── employees/
        └── children/
```

## قاعدة البيانات

### الجداول الرئيسية:
- **employees**: بيانات العمال
- **children**: بيانات الأطفال
- **ranks**: الرتب الوظيفية
- **workplaces**: أماكن العمل
- **education_levels**: المستويات التعليمية
- **bank_accounts**: الحسابات البنكية

## الاستخدام

### إضافة عامل جديد
1. انتقل إلى "إدارة العمال"
2. اضغط "إضافة عامل جديد"
3. املأ البيانات المطلوبة
4. ارفع صورة العامل (اختياري)
5. احفظ البيانات

### إضافة طفل
1. انتقل إلى صفحة العامل
2. في قسم الأطفال، اضغط "إضافة طفل"
3. املأ بيانات الطفل
4. احفظ البيانات

### إضافة حساب بنكي
1. انتقل إلى صفحة العامل
2. في قسم الحسابات البنكية، اضغط "إضافة حساب"
3. اختر نوع الحساب (بنكي/بريدي)
4. املأ البيانات المطلوبة
5. احفظ البيانات

### عرض التقارير
1. انتقل إلى قسم "التقارير"
2. اختر نوع التقرير المطلوب
3. اعرض الإحصائيات والبيانات

## الأمان

- تشفير كلمات المرور (للإصدارات المستقبلية)
- التحقق من صحة البيانات المدخلة
- حماية من رفع ملفات ضارة
- تحديد حجم الملفات المرفوعة

## التطوير المستقبلي

- [ ] نظام تسجيل الدخول والصلاحيات
- [ ] تصدير التقارير بصيغة Excel/PDF
- [ ] نظام النسخ الاحتياطي
- [ ] واجهة برمجة التطبيقات (API)
- [ ] تطبيق الهاتف المحمول
- [ ] إشعارات البريد الإلكتروني

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## الترخيص

هذا المشروع مطور خصيصاً لولاية الجلفة - الجزائر.

---

**تم التطوير بواسطة**: فريق التطوير
**التاريخ**: 2025
**الإصدار**: 1.0.0

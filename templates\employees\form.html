{% extends "base.html" %}

{% block title %}{{ title }} - نظام إدارة الخدمات الاجتماعية{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">{{ title }}</h1>
        <p class="text-muted">
            {% if employee %}
                تعديل بيانات العامل: {{ employee.full_name }}
            {% else %}
                إدخال بيانات العامل الجديد
            {% endif %}
        </p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('employees') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right"></i> العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person-fill"></i> بيانات العامل
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <!-- الصورة الشخصية -->
                        <div class="col-md-3 mb-4">
                            <div class="text-center">
                                <div class="mb-3">
                                    {% if employee and employee.photo_filename %}
                                        <img src="{{ url_for('static', filename='uploads/employees/' + employee.photo_filename) }}" 
                                             alt="صورة {{ employee.full_name }}" 
                                             class="img-thumbnail" 
                                             style="width: 200px; height: 200px; object-fit: cover;">
                                    {% else %}
                                        <div class="bg-light border rounded d-flex align-items-center justify-content-center" 
                                             style="width: 200px; height: 200px; margin: 0 auto;">
                                            <i class="bi bi-person display-1 text-muted"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.photo.label(class="form-label") }}
                                    {{ form.photo(class="form-control") }}
                                    {% if form.photo.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.photo.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- البيانات الشخصية -->
                        <div class="col-md-9">
                            <h6 class="text-primary mb-3">
                                <i class="bi bi-person-badge"></i> البيانات الشخصية
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    {{ form.first_name.label(class="form-label") }}
                                    {{ form.first_name(class="form-control") }}
                                    {% if form.first_name.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.first_name.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    {{ form.last_name.label(class="form-label") }}
                                    {{ form.last_name(class="form-control") }}
                                    {% if form.last_name.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.last_name.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    {{ form.national_id.label(class="form-label") }}
                                    {{ form.national_id(class="form-control", placeholder="18 رقم") }}
                                    {% if form.national_id.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.national_id.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    {{ form.social_security_number.label(class="form-label") }}
                                    {{ form.social_security_number(class="form-control") }}
                                    {% if form.social_security_number.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.social_security_number.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    {{ form.birth_date.label(class="form-label") }}
                                    {{ form.birth_date(class="form-control") }}
                                    {% if form.birth_date.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.birth_date.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    {{ form.phone.label(class="form-label") }}
                                    {{ form.phone(class="form-control", placeholder="0555123456") }}
                                    {% if form.phone.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.phone.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                {{ form.address.label(class="form-label") }}
                                {{ form.address(class="form-control", rows="3") }}
                                {% if form.address.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.address.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <hr class="my-4">
                    
                    <!-- البيانات الوظيفية -->
                    <h6 class="text-primary mb-3">
                        <i class="bi bi-briefcase"></i> البيانات الوظيفية
                    </h6>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.employee_number.label(class="form-label") }}
                            {{ form.employee_number(class="form-control") }}
                            {% if form.employee_number.errors %}
                                <div class="text-danger small">
                                    {% for error in form.employee_number.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.hire_date.label(class="form-label") }}
                            {{ form.hire_date(class="form-control") }}
                            {% if form.hire_date.errors %}
                                <div class="text-danger small">
                                    {% for error in form.hire_date.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.rank_id.label(class="form-label") }}
                            {{ form.rank_id(class="form-select") }}
                            {% if form.rank_id.errors %}
                                <div class="text-danger small">
                                    {% for error in form.rank_id.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.workplace_id.label(class="form-label") }}
                            {{ form.workplace_id(class="form-select") }}
                            {% if form.workplace_id.errors %}
                                <div class="text-danger small">
                                    {% for error in form.workplace_id.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <hr class="my-4">
                    
                    <!-- أزرار الحفظ -->
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('employees') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i> إلغاء
                        </a>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> {{ form.submit.label.text }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% extends "base.html" %}

{% block title %}{{ employee.full_name }} - نظام إدارة الخدمات الاجتماعية{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">{{ employee.full_name }}</h1>
        <p class="text-muted">رقم العامل: {{ employee.employee_number }}</p>
    </div>
    <div class="col-md-4 text-end">
        <div class="btn-group" role="group">
            <a href="{{ url_for('employees') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-right"></i> العودة للقائمة
            </a>
            <a href="{{ url_for('edit_employee', id=employee.id) }}" class="btn btn-warning">
                <i class="bi bi-pencil"></i> تعديل
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- البيانات الأساسية -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person-badge"></i> البيانات الأساسية
                </h5>
            </div>
            <div class="card-body text-center">
                <!-- الصورة الشخصية -->
                <div class="mb-3">
                    {% if employee.photo_filename %}
                        <img src="{{ url_for('static', filename='uploads/employees/' + employee.photo_filename) }}" 
                             alt="صورة {{ employee.full_name }}" 
                             class="img-thumbnail rounded-circle" 
                             style="width: 150px; height: 150px; object-fit: cover;">
                    {% else %}
                        <div class="bg-light border rounded-circle d-flex align-items-center justify-content-center mx-auto" 
                             style="width: 150px; height: 150px;">
                            <i class="bi bi-person display-3 text-muted"></i>
                        </div>
                    {% endif %}
                </div>
                
                <h4 class="mb-1">{{ employee.full_name }}</h4>
                <p class="text-muted mb-3">{{ employee.rank.name }}</p>
                
                <div class="row text-start">
                    <div class="col-12 mb-2">
                        <strong>الرقم الوطني:</strong><br>
                        <span class="text-muted">{{ employee.national_id }}</span>
                    </div>
                    <div class="col-12 mb-2">
                        <strong>رقم الضمان:</strong><br>
                        <span class="text-muted">{{ employee.social_security_number }}</span>
                    </div>
                    <div class="col-12 mb-2">
                        <strong>تاريخ الميلاد:</strong><br>
                        <span class="text-muted">{{ employee.birth_date.strftime('%Y-%m-%d') }}</span>
                    </div>
                    {% if employee.phone %}
                    <div class="col-12 mb-2">
                        <strong>الهاتف:</strong><br>
                        <span class="text-muted">{{ employee.phone }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- البيانات الوظيفية -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-briefcase"></i> البيانات الوظيفية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <strong>رقم العامل:</strong><br>
                        <span class="text-muted">{{ employee.employee_number }}</span>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>تاريخ التوظيف:</strong><br>
                        <span class="text-muted">{{ employee.hire_date.strftime('%Y-%m-%d') }}</span>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>الرتبة:</strong><br>
                        <span class="text-muted">{{ employee.rank.name }}</span>
                    </div>
                    <div class="col-md-6 mb-3">
                        <strong>مكان العمل:</strong><br>
                        <span class="text-muted">{{ employee.workplace.name }} ({{ employee.workplace.type }})</span>
                    </div>
                </div>
                
                {% if employee.address %}
                <div class="mt-3">
                    <strong>العنوان:</strong><br>
                    <span class="text-muted">{{ employee.address }}</span>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- الحسابات البنكية -->
        <div class="card mt-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-credit-card"></i> الحسابات البنكية
                </h5>
                <a href="{{ url_for('add_bank_account', employee_id=employee.id) }}" class="btn btn-sm btn-primary">
                    <i class="bi bi-plus"></i> إضافة حساب
                </a>
            </div>
            <div class="card-body">
                {% if bank_accounts %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>نوع الحساب</th>
                                    <th>رقم الحساب</th>
                                    <th>البنك/الوكالة</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for account in bank_accounts %}
                                <tr>
                                    <td>{{ account.account_type }}</td>
                                    <td>{{ account.account_number }}</td>
                                    <td>
                                        {% if account.bank_name %}
                                            {{ account.bank_name }}
                                            {% if account.branch_name %}
                                                - {{ account.branch_name }}
                                            {% endif %}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if account.is_active %}
                                            <span class="badge bg-success">نشط</span>
                                        {% else %}
                                            <span class="badge bg-secondary">غير نشط</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('edit_bank_account', id=account.id) }}" class="btn btn-sm btn-outline-warning">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <form method="POST" action="{{ url_for('delete_bank_account', id=account.id) }}" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا الحساب؟')">
                                            <button type="submit" class="btn btn-sm btn-outline-danger">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-3">
                        <i class="bi bi-credit-card display-4 text-muted"></i>
                        <p class="text-muted mt-2">لا توجد حسابات بنكية مسجلة</p>
                        <a href="{{ url_for('add_bank_account', employee_id=employee.id) }}" class="btn btn-primary">
                            <i class="bi bi-plus"></i> إضافة حساب بنكي
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- الأطفال -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person-hearts"></i> الأطفال
                    <span class="badge bg-primary">{{ children|length }}</span>
                </h5>
                <a href="{{ url_for('add_child', employee_id=employee.id) }}" class="btn btn-sm btn-primary">
                    <i class="bi bi-plus"></i> إضافة طفل
                </a>
            </div>
            <div class="card-body">
                {% if children %}
                    <div class="row">
                        {% for child in children %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card border">
                                <div class="card-body text-center">
                                    <!-- صورة الطفل -->
                                    <div class="mb-3">
                                        {% if child.photo_filename %}
                                            <img src="{{ url_for('static', filename='uploads/children/' + child.photo_filename) }}" 
                                                 alt="صورة {{ child.full_name }}" 
                                                 class="img-thumbnail rounded-circle" 
                                                 style="width: 80px; height: 80px; object-fit: cover;">
                                        {% else %}
                                            <div class="bg-light border rounded-circle d-flex align-items-center justify-content-center mx-auto" 
                                                 style="width: 80px; height: 80px;">
                                                <i class="bi bi-person text-muted"></i>
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <h6 class="card-title">{{ child.full_name }}</h6>
                                    <p class="text-muted small mb-2">
                                        {{ child.gender }} - {{ child.age }} سنة
                                    </p>
                                    
                                    {% if child.education_level %}
                                        <p class="text-muted small mb-2">
                                            <i class="bi bi-book"></i> {{ child.education_level.name }}
                                        </p>
                                    {% endif %}
                                    
                                    {% if child.school_name %}
                                        <p class="text-muted small mb-3">
                                            <i class="bi bi-building"></i> {{ child.school_name }}
                                        </p>
                                    {% endif %}
                                    
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('edit_child', id=child.id) }}" class="btn btn-outline-warning">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <form method="POST" action="{{ url_for('delete_child', id=child.id) }}" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا الطفل؟')">
                                            <button type="submit" class="btn btn-outline-danger">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-person-hearts display-1 text-muted"></i>
                        <h4 class="mt-3">لا توجد أطفال مسجلين</h4>
                        <p class="text-muted">لم يتم تسجيل أي أطفال لهذا العامل</p>
                        <a href="{{ url_for('add_child', employee_id=employee.id) }}" class="btn btn-primary">
                            <i class="bi bi-plus"></i> إضافة طفل
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% endblock %}

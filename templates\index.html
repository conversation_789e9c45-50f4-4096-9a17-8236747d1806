{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام إدارة الخدمات الاجتماعية{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3 mb-0">لوحة التحكم</h1>
        <p class="text-muted">مرحباً بك في نظام إدارة الخدمات الاجتماعية لولاية الجلفة</p>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-4 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="bi bi-people display-4 mb-3"></i>
                <div class="stats-number">{{ total_employees }}</div>
                <div class="h6">إجمالي العمال</div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="bi bi-person-hearts display-4 mb-3"></i>
                <div class="stats-number">{{ total_children }}</div>
                <div class="h6">إجمالي الأطفال</div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="bi bi-building display-4 mb-3"></i>
                <div class="stats-number">{{ total_workplaces }}</div>
                <div class="h6">أماكن العمل</div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning"></i> إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('add_employee') }}" class="btn btn-primary w-100">
                            <i class="bi bi-person-plus"></i> إضافة عامل جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('employees') }}" class="btn btn-outline-primary w-100">
                            <i class="bi bi-search"></i> البحث عن عامل
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-outline-success w-100">
                            <i class="bi bi-file-earmark-text"></i> إنشاء تقرير
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-outline-info w-100">
                            <i class="bi bi-download"></i> تصدير البيانات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Employees -->
{% if recent_employees %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i> آخر العمال المضافين
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>الاسم الكامل</th>
                                <th>رقم العامل</th>
                                <th>الرتبة</th>
                                <th>مكان العمل</th>
                                <th>تاريخ الإضافة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in recent_employees %}
                            <tr>
                                <td>
                                    {% if employee.photo_filename %}
                                        <img src="{{ url_for('static', filename='uploads/employees/' + employee.photo_filename) }}" 
                                             alt="صورة {{ employee.full_name }}" class="employee-photo">
                                    {% else %}
                                        <div class="employee-photo bg-secondary d-flex align-items-center justify-content-center">
                                            <i class="bi bi-person text-white"></i>
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>{{ employee.full_name }}</strong><br>
                                    <small class="text-muted">{{ employee.national_id }}</small>
                                </td>
                                <td>{{ employee.employee_number }}</td>
                                <td>{{ employee.rank.name }}</td>
                                <td>{{ employee.workplace.name }}</td>
                                <td>{{ employee.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <a href="{{ url_for('view_employee', id=employee.id) }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye"></i> عرض
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <div class="text-center mt-3">
                    <a href="{{ url_for('employees') }}" class="btn btn-outline-primary">
                        عرض جميع العمال <i class="bi bi-arrow-left"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

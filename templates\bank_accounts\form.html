{% extends "base.html" %}

{% block title %}{{ title }} - نظام إدارة الخدمات الاجتماعية{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h1 class="h3 mb-0">{{ title }}</h1>
        <p class="text-muted">
            العامل: <strong>{{ employee.full_name }}</strong> ({{ employee.employee_number }})
        </p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('view_employee', id=employee.id) }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right"></i> العودة لصفحة العامل
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-credit-card"></i> بيانات الحساب البنكي
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <!-- نوع الحساب -->
                    <div class="mb-3">
                        {{ form.account_type.label(class="form-label") }}
                        {{ form.account_type(class="form-select") }}
                        {% if form.account_type.errors %}
                            <div class="text-danger small">
                                {% for error in form.account_type.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- رقم الحساب -->
                    <div class="mb-3">
                        {{ form.account_number.label(class="form-label") }}
                        {{ form.account_number(class="form-control") }}
                        {% if form.account_number.errors %}
                            <div class="text-danger small">
                                {% for error in form.account_number.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- اسم البنك -->
                    <div class="mb-3">
                        {{ form.bank_name.label(class="form-label") }}
                        {{ form.bank_name(class="form-control", placeholder="مثال: البنك الوطني الجزائري") }}
                        {% if form.bank_name.errors %}
                            <div class="text-danger small">
                                {% for error in form.bank_name.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            اتركه فارغاً إذا كان حساباً بريدياً
                        </div>
                    </div>
                    
                    <!-- اسم الوكالة -->
                    <div class="mb-3">
                        {{ form.branch_name.label(class="form-label") }}
                        {{ form.branch_name(class="form-control", placeholder="مثال: وكالة الجلفة المركز") }}
                        {% if form.branch_name.errors %}
                            <div class="text-danger small">
                                {% for error in form.branch_name.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- حالة الحساب -->
                    <div class="mb-4">
                        <div class="form-check">
                            {{ form.is_active(class="form-check-input") }}
                            {{ form.is_active.label(class="form-check-label") }}
                        </div>
                        {% if form.is_active.errors %}
                            <div class="text-danger small">
                                {% for error in form.is_active.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <hr class="my-4">
                    
                    <!-- أزرار الحفظ -->
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('view_employee', id=employee.id) }}" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle"></i> إلغاء
                        </a>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> {{ form.submit.label.text }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
// إخفاء/إظهار حقول البنك حسب نوع الحساب
document.getElementById('account_type').addEventListener('change', function() {
    const accountType = this.value;
    const bankNameField = document.getElementById('bank_name').closest('.mb-3');
    const branchNameField = document.getElementById('branch_name').closest('.mb-3');
    
    if (accountType === 'بريدي') {
        bankNameField.style.display = 'none';
        branchNameField.querySelector('label').textContent = 'مكتب البريد';
        branchNameField.querySelector('input').placeholder = 'مثال: مكتب بريد الجلفة المركز';
    } else if (accountType === 'بنكي') {
        bankNameField.style.display = 'block';
        branchNameField.querySelector('label').textContent = 'اسم الوكالة';
        branchNameField.querySelector('input').placeholder = 'مثال: وكالة الجلفة المركز';
    }
});

// تشغيل الدالة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('account_type').dispatchEvent(new Event('change'));
});
</script>
{% endblock %}

{% extends "base.html" %}

{% block title %}التقارير والإحصائيات - نظام إدارة الخدمات الاجتماعية{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="h3 mb-0">التقارير والإحصائيات</h1>
        <p class="text-muted">عرض شامل لإحصائيات النظام والتقارير المختلفة</p>
    </div>
</div>

<!-- الإحصائيات العامة -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="bi bi-people display-4 mb-3"></i>
                <div class="stats-number">{{ total_employees }}</div>
                <div class="h6">إجمالي العمال</div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="bi bi-person-hearts display-4 mb-3"></i>
                <div class="stats-number">{{ total_children }}</div>
                <div class="h6">إجمالي الأطفال</div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="bi bi-building display-4 mb-3"></i>
                <div class="stats-number">{{ total_workplaces }}</div>
                <div class="h6">أماكن العمل</div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="bi bi-award display-4 mb-3"></i>
                <div class="stats-number">{{ total_ranks }}</div>
                <div class="h6">الرتب الوظيفية</div>
            </div>
        </div>
    </div>
</div>

<!-- التقارير التفصيلية -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-file-earmark-text"></i> التقارير التفصيلية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="bi bi-building-check display-4 text-primary mb-3"></i>
                                <h6>العمال حسب مكان العمل</h6>
                                <p class="text-muted small">عرض العمال مجمعين حسب أماكن العمل</p>
                                <a href="{{ url_for('employees_by_workplace') }}" class="btn btn-primary btn-sm">
                                    عرض التقرير
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="bi bi-book display-4 text-success mb-3"></i>
                                <h6>الأطفال حسب المستوى التعليمي</h6>
                                <p class="text-muted small">عرض الأطفال مجمعين حسب المستوى التعليمي</p>
                                <a href="{{ url_for('children_by_education') }}" class="btn btn-success btn-sm">
                                    عرض التقرير
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="bi bi-download display-4 text-info mb-3"></i>
                                <h6>تصدير البيانات</h6>
                                <p class="text-muted small">تصدير البيانات بصيغ مختلفة</p>
                                <button class="btn btn-info btn-sm" onclick="alert('سيتم تطوير هذه الميزة قريباً')">
                                    تصدير Excel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات أماكن العمل -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-building"></i> العمال حسب مكان العمل
                </h5>
            </div>
            <div class="card-body">
                {% if workplace_stats %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>مكان العمل</th>
                                    <th>النوع</th>
                                    <th>عدد العمال</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for workplace in workplace_stats %}
                                <tr>
                                    <td>{{ workplace.name }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'primary' if workplace.type == 'ولاية' else 'secondary' }}">
                                            {{ workplace.type }}
                                        </span>
                                    </td>
                                    <td>
                                        <strong>{{ workplace.employee_count }}</strong>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">لا توجد بيانات</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-award"></i> العمال حسب الرتبة
                </h5>
            </div>
            <div class="card-body">
                {% if rank_stats %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الرتبة</th>
                                    <th>عدد العمال</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for rank in rank_stats %}
                                <tr>
                                    <td>{{ rank.name }}</td>
                                    <td>
                                        <strong>{{ rank.employee_count }}</strong>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">لا توجد بيانات</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات الأطفال -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-person-hearts"></i> الأطفال حسب الجنس
                </h5>
            </div>
            <div class="card-body">
                {% if gender_stats %}
                    <div class="row text-center">
                        {% for gender in gender_stats %}
                        <div class="col-6">
                            <div class="p-3">
                                <i class="bi bi-person-{{ 'fill' if gender.gender == 'ذكر' else 'dress' }} display-4 text-{{ 'primary' if gender.gender == 'ذكر' else 'danger' }} mb-2"></i>
                                <h4>{{ gender.count }}</h4>
                                <p class="text-muted">{{ gender.gender }}</p>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted">لا توجد بيانات</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-credit-card"></i> الحسابات البنكية
                </h5>
            </div>
            <div class="card-body">
                {% if bank_account_stats %}
                    <div class="row text-center">
                        {% for account_type in bank_account_stats %}
                        <div class="col-6">
                            <div class="p-3">
                                <i class="bi bi-{{ 'bank' if account_type.account_type == 'بنكي' else 'mailbox' }} display-4 text-{{ 'success' if account_type.account_type == 'بنكي' else 'warning' }} mb-2"></i>
                                <h4>{{ account_type.count }}</h4>
                                <p class="text-muted">{{ account_type.account_type }}</p>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted">لا توجد بيانات</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

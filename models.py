from app import db
from datetime import datetime

class Employee(db.Model):
    """نموذج العامل"""
    __tablename__ = 'employees'
    
    id = db.Column(db.Integer, primary_key=True)
    # البيانات الشخصية
    first_name = db.Column(db.String(100), nullable=False)
    last_name = db.Column(db.String(100), nullable=False)
    national_id = db.Column(db.String(18), unique=True, nullable=False)
    social_security_number = db.Column(db.String(20), unique=True, nullable=False)
    birth_date = db.Column(db.Date, nullable=False)
    phone = db.Column(db.String(15), nullable=True)
    address = db.Column(db.Text, nullable=True)
    
    # البيانات الوظيفية
    employee_number = db.Column(db.String(20), unique=True, nullable=False)
    rank_id = db.Column(db.<PERSON>te<PERSON>, db.<PERSON><PERSON>('ranks.id'), nullable=False)
    workplace_id = db.Column(db.Integer, db.<PERSON><PERSON>('workplaces.id'), nullable=False)
    hire_date = db.Column(db.Date, nullable=False)
    
    # الصورة
    photo_filename = db.Column(db.String(255), nullable=True)
    
    # تواريخ النظام
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    rank = db.relationship('Rank', backref='employees')
    workplace = db.relationship('Workplace', backref='employees')
    children = db.relationship('Child', backref='employee', lazy='dynamic')
    bank_accounts = db.relationship('BankAccount', backref='employee', lazy='dynamic')
    
    def __repr__(self):
        return f'<Employee {self.first_name} {self.last_name}>'
    
    @property
    def full_name(self):
        return f'{self.first_name} {self.last_name}'

class Child(db.Model):
    """نموذج الطفل"""
    __tablename__ = 'children'
    
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    
    # البيانات الشخصية
    first_name = db.Column(db.String(100), nullable=False)
    last_name = db.Column(db.String(100), nullable=False)
    birth_date = db.Column(db.Date, nullable=False)
    gender = db.Column(db.String(10), nullable=False)  # ذكر/أنثى
    
    # البيانات الدراسية
    education_level_id = db.Column(db.Integer, db.ForeignKey('education_levels.id'), nullable=True)
    school_name = db.Column(db.String(200), nullable=True)
    academic_year = db.Column(db.String(20), nullable=True)
    
    # الصورة
    photo_filename = db.Column(db.String(255), nullable=True)
    
    # تواريخ النظام
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    education_level = db.relationship('EducationLevel', backref='children')
    
    def __repr__(self):
        return f'<Child {self.first_name} {self.last_name}>'
    
    @property
    def full_name(self):
        return f'{self.first_name} {self.last_name}'
    
    @property
    def age(self):
        today = datetime.now().date()
        return today.year - self.birth_date.year - ((today.month, today.day) < (self.birth_date.month, self.birth_date.day))

class Rank(db.Model):
    """نموذج الرتبة الوظيفية"""
    __tablename__ = 'ranks'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)
    description = db.Column(db.Text, nullable=True)
    
    def __repr__(self):
        return f'<Rank {self.name}>'

class Workplace(db.Model):
    """نموذج مكان العمل"""
    __tablename__ = 'workplaces'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    type = db.Column(db.String(50), nullable=False)  # ولاية/دائرة
    address = db.Column(db.Text, nullable=True)
    
    def __repr__(self):
        return f'<Workplace {self.name}>'

class EducationLevel(db.Model):
    """نموذج المستوى التعليمي"""
    __tablename__ = 'education_levels'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)
    order = db.Column(db.Integer, nullable=False)  # ترتيب المستوى
    
    def __repr__(self):
        return f'<EducationLevel {self.name}>'

class BankAccount(db.Model):
    """نموذج الحساب البنكي"""
    __tablename__ = 'bank_accounts'
    
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    
    # نوع الحساب
    account_type = db.Column(db.String(20), nullable=False)  # بنكي/بريدي
    
    # البيانات البنكية
    account_number = db.Column(db.String(50), nullable=False)
    bank_name = db.Column(db.String(200), nullable=True)
    branch_name = db.Column(db.String(200), nullable=True)
    
    # حالة الحساب
    is_active = db.Column(db.Boolean, default=True)
    
    # تواريخ النظام
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<BankAccount {self.account_number}>'

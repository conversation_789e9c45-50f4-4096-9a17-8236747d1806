from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_from_directory
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from werkzeug.utils import secure_filename
import os
from datetime import datetime, timezone
from PIL import Image
import uuid

from config import config

app = Flask(__name__)

# تحديد بيئة التشغيل
config_name = os.environ.get('FLASK_ENV') or 'development'
app.config.from_object(config[config_name])

# تهيئة التطبيق
config[config_name].init_app(app)

db = SQLAlchemy(app)
migrate = Migrate(app, db)

# استيراد النماذج والفورمز بعد إنشاء db
from models import Employee, Child, Rank, Workplace, EducationLevel, BankAccount
from forms import EmployeeForm, ChildForm, BankAccountForm, SearchForm, RankForm, WorkplaceForm, EducationLevelForm

def allowed_file(filename):
    """التحقق من امتداد الملف"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

def save_picture(form_picture, folder):
    """حفظ الصورة وإرجاع اسم الملف"""
    if form_picture and allowed_file(form_picture.filename):
        # إنشاء اسم ملف فريد
        random_hex = uuid.uuid4().hex
        _, f_ext = os.path.splitext(form_picture.filename)
        picture_fn = random_hex + f_ext
        picture_path = os.path.join(app.root_path, 'static', 'uploads', folder, picture_fn)

        # تصغير حجم الصورة
        output_size = app.config['IMAGE_THUMBNAIL_SIZE']
        img = Image.open(form_picture)
        img.thumbnail(output_size)
        img.save(picture_path)

        return picture_fn
    return None

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    # إحصائيات سريعة
    total_employees = Employee.query.count()
    total_children = Child.query.count()
    total_workplaces = Workplace.query.count()

    # أحدث العمال المضافين
    recent_employees = Employee.query.order_by(Employee.created_at.desc()).limit(5).all()

    return render_template('index.html',
                         total_employees=total_employees,
                         total_children=total_children,
                         total_workplaces=total_workplaces,
                         recent_employees=recent_employees)

@app.route('/employees')
def employees():
    """قائمة العمال"""
    page = request.args.get('page', 1, type=int)
    search_form = SearchForm()

    query = Employee.query

    # البحث
    if request.args.get('search_term'):
        search_term = request.args.get('search_term')
        search_type = request.args.get('search_type', 'name')

        if search_type == 'name':
            query = query.filter(
                (Employee.first_name.contains(search_term)) |
                (Employee.last_name.contains(search_term))
            )
        elif search_type == 'national_id':
            query = query.filter(Employee.national_id.contains(search_term))
        elif search_type == 'employee_number':
            query = query.filter(Employee.employee_number.contains(search_term))
        elif search_type == 'workplace':
            query = query.join(Workplace).filter(Workplace.name.contains(search_term))
        elif search_type == 'rank':
            query = query.join(Rank).filter(Rank.name.contains(search_term))

    employees = query.order_by(Employee.last_name, Employee.first_name).paginate(
        page=page, per_page=app.config['ITEMS_PER_PAGE'], error_out=False)

    return render_template('employees/list.html', employees=employees, search_form=search_form)

@app.route('/employee/add', methods=['GET', 'POST'])
def add_employee():
    """إضافة عامل جديد"""
    form = EmployeeForm()

    if form.validate_on_submit():
        # التحقق من عدم تكرار الرقم الوطني
        if Employee.query.filter_by(national_id=form.national_id.data).first():
            flash('الرقم الوطني موجود مسبقاً!', 'error')
            return render_template('employees/form.html', form=form, title='إضافة عامل جديد')

        # التحقق من عدم تكرار رقم العامل
        if Employee.query.filter_by(employee_number=form.employee_number.data).first():
            flash('رقم العامل موجود مسبقاً!', 'error')
            return render_template('employees/form.html', form=form, title='إضافة عامل جديد')

        # حفظ الصورة
        photo_filename = None
        if form.photo.data:
            photo_filename = save_picture(form.photo.data, 'employees')

        # إنشاء العامل الجديد
        employee = Employee(
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            national_id=form.national_id.data,
            social_security_number=form.social_security_number.data,
            birth_date=form.birth_date.data,
            phone=form.phone.data,
            address=form.address.data,
            employee_number=form.employee_number.data,
            rank_id=form.rank_id.data,
            workplace_id=form.workplace_id.data,
            hire_date=form.hire_date.data,
            photo_filename=photo_filename
        )

        db.session.add(employee)
        db.session.commit()

        flash('تم إضافة العامل بنجاح!', 'success')
        return redirect(url_for('employees'))

    return render_template('employees/form.html', form=form, title='إضافة عامل جديد')

@app.route('/employee/<int:id>')
def view_employee(id):
    """عرض تفاصيل العامل"""
    employee = Employee.query.get_or_404(id)
    children = employee.children.all()
    bank_accounts = employee.bank_accounts.all()

    return render_template('employees/view.html',
                         employee=employee,
                         children=children,
                         bank_accounts=bank_accounts)

@app.route('/employee/<int:id>/edit', methods=['GET', 'POST'])
def edit_employee(id):
    """تعديل بيانات العامل"""
    employee = Employee.query.get_or_404(id)
    form = EmployeeForm(obj=employee)

    if form.validate_on_submit():
        # التحقق من عدم تكرار الرقم الوطني (باستثناء العامل الحالي)
        existing_employee = Employee.query.filter_by(national_id=form.national_id.data).first()
        if existing_employee and existing_employee.id != employee.id:
            flash('الرقم الوطني موجود مسبقاً!', 'error')
            return render_template('employees/form.html', form=form, title='تعديل بيانات العامل')

        # التحقق من عدم تكرار رقم العامل (باستثناء العامل الحالي)
        existing_employee = Employee.query.filter_by(employee_number=form.employee_number.data).first()
        if existing_employee and existing_employee.id != employee.id:
            flash('رقم العامل موجود مسبقاً!', 'error')
            return render_template('employees/form.html', form=form, title='تعديل بيانات العامل')

        # حفظ الصورة الجديدة إذا تم رفعها
        if form.photo.data:
            photo_filename = save_picture(form.photo.data, 'employees')
            employee.photo_filename = photo_filename

        # تحديث البيانات
        form.populate_obj(employee)
        employee.updated_at = datetime.now(timezone.utc)

        db.session.commit()
        flash('تم تحديث بيانات العامل بنجاح!', 'success')
        return redirect(url_for('view_employee', id=employee.id))

    return render_template('employees/form.html', form=form, title='تعديل بيانات العامل', employee=employee)

@app.route('/employee/<int:id>/delete', methods=['POST'])
def delete_employee(id):
    """حذف العامل"""
    employee = Employee.query.get_or_404(id)

    # حذف الصورة من الخادم
    if employee.photo_filename:
        photo_path = os.path.join(app.root_path, 'static', 'uploads', 'employees', employee.photo_filename)
        if os.path.exists(photo_path):
            os.remove(photo_path)

    db.session.delete(employee)
    db.session.commit()

    flash('تم حذف العامل بنجاح!', 'success')
    return redirect(url_for('employees'))

# مسارات إدارة الأطفال
@app.route('/employee/<int:employee_id>/child/add', methods=['GET', 'POST'])
def add_child(employee_id):
    """إضافة طفل للعامل"""
    employee = Employee.query.get_or_404(employee_id)
    form = ChildForm()

    if form.validate_on_submit():
        # حفظ الصورة
        photo_filename = None
        if form.photo.data:
            photo_filename = save_picture(form.photo.data, 'children')

        # إنشاء الطفل الجديد
        child = Child(
            employee_id=employee.id,
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            birth_date=form.birth_date.data,
            gender=form.gender.data,
            education_level_id=form.education_level_id.data if form.education_level_id.data != 0 else None,
            school_name=form.school_name.data,
            academic_year=form.academic_year.data,
            photo_filename=photo_filename
        )

        db.session.add(child)
        db.session.commit()

        flash('تم إضافة الطفل بنجاح!', 'success')
        return redirect(url_for('view_employee', id=employee.id))

    return render_template('children/form.html', form=form, employee=employee, title='إضافة طفل')

@app.route('/child/<int:id>/edit', methods=['GET', 'POST'])
def edit_child(id):
    """تعديل بيانات الطفل"""
    child = Child.query.get_or_404(id)
    form = ChildForm(obj=child)

    if form.validate_on_submit():
        # حفظ الصورة الجديدة إذا تم رفعها
        if form.photo.data:
            photo_filename = save_picture(form.photo.data, 'children')
            child.photo_filename = photo_filename

        # تحديث البيانات
        form.populate_obj(child)
        child.updated_at = datetime.now(timezone.utc)

        # تعيين education_level_id إلى None إذا كان 0
        if child.education_level_id == 0:
            child.education_level_id = None

        db.session.commit()
        flash('تم تحديث بيانات الطفل بنجاح!', 'success')
        return redirect(url_for('view_employee', id=child.employee_id))

    return render_template('children/form.html', form=form, child=child, employee=child.employee, title='تعديل بيانات الطفل')

@app.route('/child/<int:id>/delete', methods=['POST'])
def delete_child(id):
    """حذف الطفل"""
    child = Child.query.get_or_404(id)
    employee_id = child.employee_id

    # حذف الصورة من الخادم
    if child.photo_filename:
        photo_path = os.path.join(app.root_path, 'static', 'uploads', 'children', child.photo_filename)
        if os.path.exists(photo_path):
            os.remove(photo_path)

    db.session.delete(child)
    db.session.commit()

    flash('تم حذف الطفل بنجاح!', 'success')
    return redirect(url_for('view_employee', id=employee_id))

# مسارات إدارة الحسابات البنكية
@app.route('/employee/<int:employee_id>/bank_account/add', methods=['GET', 'POST'])
def add_bank_account(employee_id):
    """إضافة حساب بنكي للعامل"""
    employee = Employee.query.get_or_404(employee_id)
    form = BankAccountForm()

    if form.validate_on_submit():
        # إنشاء الحساب البنكي الجديد
        bank_account = BankAccount(
            employee_id=employee.id,
            account_type=form.account_type.data,
            account_number=form.account_number.data,
            bank_name=form.bank_name.data,
            branch_name=form.branch_name.data,
            is_active=form.is_active.data
        )

        db.session.add(bank_account)
        db.session.commit()

        flash('تم إضافة الحساب البنكي بنجاح!', 'success')
        return redirect(url_for('view_employee', id=employee.id))

    return render_template('bank_accounts/form.html', form=form, employee=employee, title='إضافة حساب بنكي')

@app.route('/bank_account/<int:id>/edit', methods=['GET', 'POST'])
def edit_bank_account(id):
    """تعديل الحساب البنكي"""
    bank_account = BankAccount.query.get_or_404(id)
    form = BankAccountForm(obj=bank_account)

    if form.validate_on_submit():
        # تحديث البيانات
        form.populate_obj(bank_account)
        bank_account.updated_at = datetime.now(timezone.utc)

        db.session.commit()
        flash('تم تحديث الحساب البنكي بنجاح!', 'success')
        return redirect(url_for('view_employee', id=bank_account.employee_id))

    return render_template('bank_accounts/form.html', form=form, bank_account=bank_account, employee=bank_account.employee, title='تعديل الحساب البنكي')

@app.route('/bank_account/<int:id>/delete', methods=['POST'])
def delete_bank_account(id):
    """حذف الحساب البنكي"""
    bank_account = BankAccount.query.get_or_404(id)
    employee_id = bank_account.employee_id

    db.session.delete(bank_account)
    db.session.commit()

    flash('تم حذف الحساب البنكي بنجاح!', 'success')
    return redirect(url_for('view_employee', id=employee_id))

# مسارات التقارير والإحصائيات
@app.route('/reports')
def reports():
    """صفحة التقارير والإحصائيات"""
    # إحصائيات عامة
    total_employees = Employee.query.count()
    total_children = Child.query.count()
    total_workplaces = Workplace.query.count()
    total_ranks = Rank.query.count()

    # إحصائيات حسب مكان العمل
    workplace_stats = db.session.query(
        Workplace.name,
        Workplace.type,
        db.func.count(Employee.id).label('employee_count')
    ).join(Employee).group_by(Workplace.id).all()

    # إحصائيات حسب الرتبة
    rank_stats = db.session.query(
        Rank.name,
        db.func.count(Employee.id).label('employee_count')
    ).join(Employee).group_by(Rank.id).order_by(db.func.count(Employee.id).desc()).all()

    # إحصائيات الأطفال حسب المستوى التعليمي
    education_stats = db.session.query(
        EducationLevel.name,
        db.func.count(Child.id).label('child_count')
    ).join(Child).group_by(EducationLevel.id).order_by(EducationLevel.order).all()

    # إحصائيات الأطفال حسب الجنس
    gender_stats = db.session.query(
        Child.gender,
        db.func.count(Child.id).label('count')
    ).group_by(Child.gender).all()

    # إحصائيات الحسابات البنكية
    bank_account_stats = db.session.query(
        BankAccount.account_type,
        db.func.count(BankAccount.id).label('count')
    ).group_by(BankAccount.account_type).all()

    return render_template('reports/index.html',
                         total_employees=total_employees,
                         total_children=total_children,
                         total_workplaces=total_workplaces,
                         total_ranks=total_ranks,
                         workplace_stats=workplace_stats,
                         rank_stats=rank_stats,
                         education_stats=education_stats,
                         gender_stats=gender_stats,
                         bank_account_stats=bank_account_stats)

@app.route('/reports/employees_by_workplace')
def employees_by_workplace():
    """تقرير العمال حسب مكان العمل"""
    workplace_id = request.args.get('workplace_id', type=int)

    workplaces = Workplace.query.all()
    employees = []

    if workplace_id:
        workplace = Workplace.query.get_or_404(workplace_id)
        employees = Employee.query.filter_by(workplace_id=workplace_id).order_by(Employee.last_name, Employee.first_name).all()

    return render_template('reports/employees_by_workplace.html',
                         workplaces=workplaces,
                         employees=employees,
                         selected_workplace_id=workplace_id)

@app.route('/reports/children_by_education')
def children_by_education():
    """تقرير الأطفال حسب المستوى التعليمي"""
    education_level_id = request.args.get('education_level_id', type=int)

    education_levels = EducationLevel.query.order_by(EducationLevel.order).all()
    children = []

    if education_level_id:
        education_level = EducationLevel.query.get_or_404(education_level_id)
        children = Child.query.filter_by(education_level_id=education_level_id).order_by(Child.last_name, Child.first_name).all()

    return render_template('reports/children_by_education.html',
                         education_levels=education_levels,
                         children=children,
                         selected_education_level_id=education_level_id)

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True, host='0.0.0.0', port=5000)

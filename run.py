#!/usr/bin/env python
# -*- coding: utf-8 -*-

try:
    from app import app, db
    print("✓ تم استيراد التطبيق بنجاح")

    if __name__ == '__main__':
        with app.app_context():
            db.create_all()
            print("✓ تم إنشاء جداول قاعدة البيانات بنجاح")

        print("🚀 بدء تشغيل تطبيق Flask...")
        print("🌐 يمكنك الوصول للتطبيق على: http://localhost:5000")
        print("📋 للتوقف اضغط Ctrl+C")
        print("-" * 50)

        app.run(debug=True, host='0.0.0.0', port=5000)

except Exception as e:
    print(f"❌ خطأ في تشغيل التطبيق: {e}")
    import traceback
    traceback.print_exc()

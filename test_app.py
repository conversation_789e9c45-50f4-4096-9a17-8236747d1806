#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
اختبار سريع للتطبيق
"""

def test_imports():
    """اختبار استيراد المكتبات"""
    try:
        from app import app, db
        from models import Employee, Child, Rank, Workplace, EducationLevel, BankAccount
        from forms import EmployeeForm, ChildForm, BankAccountForm
        print("✅ تم استيراد جميع المكتبات بنجاح")
        return True
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    try:
        from app import app, db
        from models import Employee, Rank, Workplace
        
        with app.app_context():
            # اختبار الاتصال بقاعدة البيانات
            total_employees = Employee.query.count()
            total_ranks = Rank.query.count()
            total_workplaces = Workplace.query.count()
            
            print(f"✅ قاعدة البيانات تعمل بنجاح")
            print(f"   - عدد العمال: {total_employees}")
            print(f"   - عدد الرتب: {total_ranks}")
            print(f"   - عدد أماكن العمل: {total_workplaces}")
            return True
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_routes():
    """اختبار المسارات"""
    try:
        from app import app
        
        with app.test_client() as client:
            # اختبار الصفحة الرئيسية
            response = client.get('/')
            if response.status_code == 200:
                print("✅ الصفحة الرئيسية تعمل")
            else:
                print(f"❌ خطأ في الصفحة الرئيسية: {response.status_code}")
                return False
            
            # اختبار صفحة العمال
            response = client.get('/employees')
            if response.status_code == 200:
                print("✅ صفحة العمال تعمل")
            else:
                print(f"❌ خطأ في صفحة العمال: {response.status_code}")
                return False
            
            # اختبار صفحة التقارير
            response = client.get('/reports')
            if response.status_code == 200:
                print("✅ صفحة التقارير تعمل")
            else:
                print(f"❌ خطأ في صفحة التقارير: {response.status_code}")
                return False
            
            return True
    except Exception as e:
        print(f"❌ خطأ في اختبار المسارات: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار التطبيق...")
    print("-" * 50)
    
    tests = [
        ("استيراد المكتبات", test_imports),
        ("قاعدة البيانات", test_database),
        ("المسارات", test_routes)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 اختبار {test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"❌ فشل اختبار {test_name}")
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! التطبيق جاهز للاستخدام")
        print("🌐 يمكنك تشغيل التطبيق باستخدام: python run.py")
        print("🔗 ثم الانتقال إلى: http://localhost:5000")
    else:
        print("⚠️  بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")

if __name__ == '__main__':
    main()
